([('FastFlasher.exe',
   'D:\\software\\code\\FlashToolApp\\build\\FastFlasher\\FastFlasher.exe',
   'EXECUTABLE'),
  ('hypsensor\\ComPortDll.dll',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\ComPortDll.dll',
   'BINARY'),
  ('hypsensor\\hyp225-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp225-mcuboot.exe',
   'BINARY'),
  ('hypsensor\\hyp225v2-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp225v2-mcuboot.exe',
   'BINARY'),
  ('hypsensor\\hyp225v3-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp225v3-mcuboot.exe',
   'BINARY'),
  ('hypsensor\\hyp60-bc.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60-bc.exe',
   'BINARY'),
  ('hypsensor\\hyp60-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60-mcuboot.exe',
   'BINARY'),
  ('hypsensor\\hyp60.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60.exe',
   'BINARY'),
  ('hypsensor\\hyp60v2-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60v2-mcuboot.exe',
   'BINARY'),
  ('hypsensor\\hyp60v3-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60v3-mcuboot.exe',
   'BINARY'),
  ('hypsensor\\hyp60v4-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60v4-mcuboot.exe',
   'BINARY'),
  ('hypsensor\\hypx-mcuboot.exe',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hypx-mcuboot.exe',
   'BINARY'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\json.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\join.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\index.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\MSVCP140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\zlib1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('certificate\\AmazonRootCA1.pem',
   'D:\\software\\code\\FlashToolApp\\certificate\\AmazonRootCA1.pem',
   'DATA'),
  ('certificate\\device_cert.pem.crt',
   'D:\\software\\code\\FlashToolApp\\certificate\\device_cert.pem.crt',
   'DATA'),
  ('certificate\\device_private.pem.key',
   'D:\\software\\code\\FlashToolApp\\certificate\\device_private.pem.key',
   'DATA'),
  ('configure_esp_secure_cert.py',
   'D:\\software\\code\\FlashToolApp\\configure_esp_secure_cert.py',
   'DATA'),
  ('esp-idf\\.codespellrc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.codespellrc',
   'DATA'),
  ('esp-idf\\.editorconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.editorconfig',
   'DATA'),
  ('esp-idf\\.flake8',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.flake8',
   'DATA'),
  ('esp-idf\\.gitignore',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.gitignore',
   'DATA'),
  ('esp-idf\\.gitlab-ci.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.gitlab-ci.yml',
   'DATA'),
  ('esp-idf\\.gitmodules',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.gitmodules',
   'DATA'),
  ('esp-idf\\.mypy.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.mypy.ini',
   'DATA'),
  ('esp-idf\\.pre-commit-config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.pre-commit-config.yaml',
   'DATA'),
  ('esp-idf\\.pylintrc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.pylintrc',
   'DATA'),
  ('esp-idf\\.readthedocs.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.readthedocs.yml',
   'DATA'),
  ('esp-idf\\.shellcheckrc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.shellcheckrc',
   'DATA'),
  ('esp-idf\\.vale.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\.vale.ini',
   'DATA'),
  ('esp-idf\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\Kconfig',
   'DATA'),
  ('esp-idf\\LICENSE',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\LICENSE',
   'DATA'),
  ('esp-idf\\add_path.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\add_path.sh',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\Kconfig.projbuild',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\Kconfig.projbuild',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\LICENSE',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\LICENSE',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\espefuse.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\espefuse.cmake',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\esptool\\espefuse.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\esptool\\espefuse.py',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\esptool\\espsecure.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\esptool\\espsecure.py',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\esptool\\esptool.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\esptool\\esptool.py',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\flasher_args.json.in',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\flasher_args.json.in',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\get_port_args.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\get_port_args.cmake',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\project_include.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\project_include.cmake',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\run_serial_tool.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\run_serial_tool.cmake',
   'DATA'),
  ('esp-idf\\components\\esptool_py\\sdkconfig.rename',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\esptool_py\\sdkconfig.rename',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\.gitignore',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\.gitignore',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\Kconfig',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\README.md',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\idf_component.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\idf_component.yml',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_fixtures.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_fixtures.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_cxx_api.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_cxx_api.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_handle.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_handle.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_initialization.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_initialization.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_storage.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_nvs_storage.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_partition_manager.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\main\\test_partition_manager.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\pytest_nvs_host_linux.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\pytest_nvs_host_linux.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\sdkconfig.ci.default_set_key',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\sdkconfig.ci.default_set_key',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\sdkconfig.ci.legacy_set_key',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\sdkconfig.ci.legacy_set_key',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_host_test\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\README.md',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\main\\nvs_page_test.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\main\\nvs_page_test.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\main\\test_fixtures.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\main\\test_fixtures.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\pytest_nvs_page_linux.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\pytest_nvs_page_linux.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\host_test\\nvs_page_test\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\include\\nvs.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\include\\nvs.h',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\include\\nvs_flash.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\include\\nvs_flash.h',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\include\\nvs_handle.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\include\\nvs_handle.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\README.rst',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\README.rst',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\README_CN.rst',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\README_CN.rst',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\__pycache__\\nvs_partition_gen.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\__pycache__\\nvs_partition_gen.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\__pycache__\\nvs_partition_gen.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\__pycache__\\nvs_partition_gen.cpython-312.pyc',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\nvs_partition_gen.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\nvs_partition_gen.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\part_old_blob_format.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\part_old_blob_format.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\sample_multipage_blob.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\sample_multipage_blob.csv',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\sample_singlepage_blob.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\sample_singlepage_blob.csv',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\sample_val.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\sample_val.csv',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample.base64',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample.base64',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample.hex',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample.hex',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_blob.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_blob.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_encryption_keys.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_encryption_keys.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_encryption_keys_hmac.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_encryption_keys_hmac.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_hmac_key.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_hmac_key.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_multipage_blob.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_multipage_blob.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_singlepage_blob.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_generator\\testdata\\sample_singlepage_blob.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_tool\\README.rst',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_tool\\README.rst',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_tool\\README_CN.rst',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_tool\\README_CN.rst',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_check.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_check.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_logger.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_logger.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_parser.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_parser.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_tool.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\nvs_partition_tool\\nvs_tool.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\private_include\\nvs_internal.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\private_include\\nvs_internal.h',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\project_include.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\project_include.cmake',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\compressed_enum_table.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\compressed_enum_table.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\intrusive_list.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\intrusive_list.h',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_api.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_api.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_cxx_api.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_cxx_api.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_encrypted_partition.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_encrypted_partition.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_encrypted_partition.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_encrypted_partition.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_handle_locked.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_handle_locked.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_handle_locked.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_handle_locked.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_handle_simple.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_handle_simple.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_handle_simple.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_handle_simple.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_item_hash_list.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_item_hash_list.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_item_hash_list.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_item_hash_list.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_memory_management.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_memory_management.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_page.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_page.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_page.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_page.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_pagemanager.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_pagemanager.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_pagemanager.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_pagemanager.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_partition.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_partition.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_partition.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_partition.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_partition_lookup.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_partition_lookup.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_partition_lookup.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_partition_lookup.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_partition_manager.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_partition_manager.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_partition_manager.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_partition_manager.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_platform.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_platform.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_platform.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_platform.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_storage.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_storage.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_storage.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_storage.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_test_api.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_test_api.h',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_types.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_types.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\nvs_types.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\nvs_types.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\src\\partition.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\src\\partition.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\README.md',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\main\\app_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\main\\app_main.c',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\main\\encryption_keys.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\main\\encryption_keys.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\main\\partition_encrypted.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\main\\partition_encrypted.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\main\\partition_encrypted_hmac.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\main\\partition_encrypted_hmac.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\main\\sample.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\main\\sample.bin',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\main\\test_nvs.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\main\\test_nvs.c',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\partitions_nvs_encr_flash_enc.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\partitions_nvs_encr_flash_enc.csv',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\pytest_nvs_flash.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\pytest_nvs_flash.py',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.default',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.default',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_flash_enc_esp32',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_flash_enc_esp32',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_flash_enc_esp32c3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_flash_enc_esp32c3',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_hmac_esp32c3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_hmac_esp32c3',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_hmac_no_cfg_esp32c3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.ci.nvs_encr_hmac_no_cfg_esp32c3',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_apps\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\Makefile',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\Makefile',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\README.md',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\main.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\main.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\sdkconfig.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\sdkconfig.h',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\spi_flash_emulation.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\spi_flash_emulation.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\spi_flash_emulation.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\spi_flash_emulation.h',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\test_compressed_enum_table.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\test_compressed_enum_table.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\test_fixtures.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\test_fixtures.hpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\test_intrusive_list.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\test_intrusive_list.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\test_nvs.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\test_nvs.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\test_nvs_partition.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\test_nvs_partition.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\test_partition_manager.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\test_partition_manager.cpp',
   'DATA'),
  ('esp-idf\\components\\nvs_flash\\test_nvs_host\\test_spi_flash_emulation.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\components\\nvs_flash\\test_nvs_host\\test_spi_flash_emulation.cpp',
   'DATA'),
  ('esp-idf\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\conftest.py',
   'DATA'),
  ('esp-idf\\export.bat',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\export.bat',
   'DATA'),
  ('esp-idf\\export.fish',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\export.fish',
   'DATA'),
  ('esp-idf\\export.ps1',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\export.ps1',
   'DATA'),
  ('esp-idf\\export.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\export.sh',
   'DATA'),
  ('esp-idf\\install.bat',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\install.bat',
   'DATA'),
  ('esp-idf\\install.fish',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\install.fish',
   'DATA'),
  ('esp-idf\\install.ps1',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\install.ps1',
   'DATA'),
  ('esp-idf\\install.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\install.sh',
   'DATA'),
  ('esp-idf\\pytest.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\pytest.ini',
   'DATA'),
  ('esp-idf\\sdkconfig.rename',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\sdkconfig.rename',
   'DATA'),
  ('esp-idf\\sgconfig.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\sgconfig.yml',
   'DATA'),
  ('esp-idf\\sonar-project.properties',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\sonar-project.properties',
   'DATA'),
  ('esp-idf\\tools\\__pycache__\\python_version_checker.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\__pycache__\\python_version_checker.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\__pycache__\\python_version_checker.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\__pycache__\\python_version_checker.cpython-312.pyc',
   'DATA'),
  ('esp-idf\\tools\\ble\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ble\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ble\\lib_ble_client.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ble\\lib_ble_client.py',
   'DATA'),
  ('esp-idf\\tools\\ble\\lib_gap.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ble\\lib_gap.py',
   'DATA'),
  ('esp-idf\\tools\\ble\\lib_gatt.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ble\\lib_gatt.py',
   'DATA'),
  ('esp-idf\\tools\\catch\\LICENSE.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\catch\\LICENSE.txt',
   'DATA'),
  ('esp-idf\\tools\\catch\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\catch\\README.md',
   'DATA'),
  ('esp-idf\\tools\\catch\\catch.hpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\catch\\catch.hpp',
   'DATA'),
  ('esp-idf\\tools\\check_python_dependencies.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\check_python_dependencies.py',
   'DATA'),
  ('esp-idf\\tools\\check_term.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\check_term.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\artifacts_handler.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\artifacts_handler.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\astyle-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\astyle-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\build_template_app.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\build_template_app.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_api_violation.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_api_violation.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_blobs.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_blobs.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_build_test_rules.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_build_test_rules.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_callgraph.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_callgraph.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_codeowners.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_codeowners.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_copyright_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_copyright_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_copyright_ignore.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_copyright_ignore.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_deprecated_kconfigs.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_deprecated_kconfigs.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_esp_memory_utils_headers.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_esp_memory_utils_headers.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_examples_extra_component_dirs.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_examples_extra_component_dirs.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_executables.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_executables.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_idf_version.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_idf_version.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_kconfigs.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_kconfigs.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_ldgen_mapping_exceptions.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_ldgen_mapping_exceptions.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_public_headers.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_public_headers.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_public_headers_exceptions.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_public_headers_exceptions.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_readme_links.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_readme_links.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_requirement_files.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_requirement_files.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_rules_components_patterns.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_rules_components_patterns.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_soc_headers_leak.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_soc_headers_leak.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_soc_struct_headers.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_soc_struct_headers.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_tools_files_patterns.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_tools_files_patterns.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\check_type_comments.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\check_type_comments.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\checkout_project_ref.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\checkout_project_ref.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\ci_build_apps.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\ci_build_apps.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\ci_fetch_submodule.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\ci_fetch_submodule.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\ci_get_mr_info.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\ci_get_mr_info.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\clang_tidy_dirs.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\clang_tidy_dirs.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\cleanup_ignore_lists.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\cleanup_ignore_lists.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\configure_ci_environment.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\configure_ci_environment.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\deploy_docs.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\deploy_docs.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\constants.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\constants.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\models.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\models.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\report.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\report.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\child_pipeline_build_apps.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\child_pipeline_build_apps.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_build_child_pipeline.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_build_child_pipeline.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_build_report.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_build_report.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_target_test_child_pipeline.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_target_test_child_pipeline.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_target_test_report.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\scripts\\generate_target_test_report.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\.dynamic_jobs.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\.dynamic_jobs.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\generate_target_test_report.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\generate_target_test_report.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\known_generate_test_child_pipeline_warnings.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\known_generate_test_child_pipeline_warnings.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\report.template.html',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\report.template.html',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\test_child_pipeline.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\templates\\test_child_pipeline.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\dynamic_pipelines\\utils.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\dynamic_pipelines\\utils.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\envsubst.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\envsubst.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\exclude_check_tools_files.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\exclude_check_tools_files.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\executable-list.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\executable-list.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\fix_empty_prototypes.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\fix_empty_prototypes.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\generate_rules.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\generate_rules.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\get-full-sources.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\get-full-sources.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\get_all_test_results.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\get_all_test_results.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\get_known_failure_cases_file.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\get_known_failure_cases_file.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\get_supported_examples.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\get_supported_examples.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\gitlab_yaml_linter.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\gitlab_yaml_linter.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_ci\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_ci\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_ci\\app.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_ci\\app.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_ci\\uploader.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_ci\\uploader.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_ci_utils.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_ci_utils.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\constants.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\constants.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\plugin.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\plugin.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\pytest.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\pytest.ini',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\script.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\script.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\tests\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\tests\\conftest.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\tests\\test_get_all_apps.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\tests\\test_get_all_apps.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\tests\\test_get_pytest_cases.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\tests\\test_get_pytest_cases.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\idf_pytest\\utils.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\idf_pytest\\utils.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\ignore_build_warnings.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\ignore_build_warnings.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\mirror-submodule-update.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\mirror-submodule-update.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\multirun_with_pyenv.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\multirun_with_pyenv.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\mypy_ignore_list.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\mypy_ignore_list.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\push_to_github.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\push_to_github.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\common_test_methods.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\common_test_methods.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\gitlab_api.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\gitlab_api.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\adder.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\adder.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\client.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\client.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\test.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_http_server_test\\test.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\Attenuator.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\Attenuator.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\IperfUtility.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\IperfUtility.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\LineChart.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\LineChart.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\PowerControl.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\PowerControl.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\TestReport.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\TestReport.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\idf_iperf_test_util\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\python_packages\\wifi_tools.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\python_packages\\wifi_tools.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\sg_rules\\no_private_rom_api_in_examples.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\sg_rules\\no_private_rom_api_in_examples.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\sg_rules\\no_std_assert_in_hal_component.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\sg_rules\\no_std_assert_in_hal_component.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\sonar_exclude_list.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\sonar_exclude_list.txt',
   'DATA'),
  ('esp-idf\\tools\\ci\\sort_yaml.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\sort_yaml.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\static-analysis-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\static-analysis-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\ci\\test_autocomplete\\pytest.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\test_autocomplete\\pytest.ini',
   'DATA'),
  ('esp-idf\\tools\\ci\\test_autocomplete\\test_autocomplete.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\test_autocomplete\\test_autocomplete.py',
   'DATA'),
  ('esp-idf\\tools\\ci\\test_configure_ci_environment.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\test_configure_ci_environment.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\test_reproducible_build.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\test_reproducible_build.sh',
   'DATA'),
  ('esp-idf\\tools\\ci\\utils.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ci\\utils.sh',
   'DATA'),
  ('esp-idf\\tools\\cmake\\build.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\build.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\component.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\component.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\component_deps.dot.in',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\component_deps.dot.in',
   'DATA'),
  ('esp-idf\\tools\\cmake\\depgraph.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\depgraph.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\dfu.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\dfu.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\git_submodules.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\git_submodules.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\idf.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\idf.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\kconfig.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\kconfig.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\ldgen.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\ldgen.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\project.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\project.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\project_description.json.in',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\project_description.json.in',
   'DATA'),
  ('esp-idf\\tools\\cmake\\run_dfu_util.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\run_dfu_util.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\run_size_tool.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\run_size_tool.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\scripts\\component_get_requirements.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\scripts\\component_get_requirements.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\scripts\\data_file_embed_asm.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\scripts\\data_file_embed_asm.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\scripts\\fail.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\scripts\\fail.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\targets.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\targets.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\third_party\\GetGitRevisionDescription.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\third_party\\GetGitRevisionDescription.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\third_party\\GetGitRevisionDescription.cmake.in',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\third_party\\GetGitRevisionDescription.cmake.in',
   'DATA'),
  ('esp-idf\\tools\\cmake\\tool_version_check.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\tool_version_check.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32c2.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32c2.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32c3.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32c3.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32c6.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32c6.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32h2.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32h2.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32h4.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32h4.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32s2.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32s2.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-clang-esp32s3.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-clang-esp32s3.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32c2.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32c2.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32c3.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32c3.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32c5.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32c5.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32c6.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32c6.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32c61.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32c61.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32h2.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32h2.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32p4.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32p4.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32s2.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32s2.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-esp32s3.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-esp32s3.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\toolchain-linux.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\toolchain-linux.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\utilities.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\utilities.cmake',
   'DATA'),
  ('esp-idf\\tools\\cmake\\version.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\cmake\\version.cmake',
   'DATA'),
  ('esp-idf\\tools\\detect_python.fish',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\detect_python.fish',
   'DATA'),
  ('esp-idf\\tools\\detect_python.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\detect_python.sh',
   'DATA'),
  ('esp-idf\\tools\\docker\\Dockerfile',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\docker\\Dockerfile',
   'DATA'),
  ('esp-idf\\tools\\docker\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\docker\\README.md',
   'DATA'),
  ('esp-idf\\tools\\docker\\entrypoint.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\docker\\entrypoint.sh',
   'DATA'),
  ('esp-idf\\tools\\eclipse-code-style.xml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\eclipse-code-style.xml',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\SYSVIEW_FreeRTOS.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\SYSVIEW_FreeRTOS.txt',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\espytrace\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\espytrace\\README.md',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\espytrace\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\espytrace\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\espytrace\\apptrace.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\espytrace\\apptrace.py',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\espytrace\\sysview.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\espytrace\\sysview.py',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\logtrace_proc.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\logtrace_proc.py',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\sysviewtrace_proc.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\sysviewtrace_proc.py',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\logtrace\\adc_log.trc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\logtrace\\adc_log.trc',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\logtrace\\expected_output',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\logtrace\\expected_output',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\logtrace\\test.elf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\logtrace\\test.elf',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\logtrace\\test.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\logtrace\\test.sh',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\blink.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\blink.c',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\cpu0.svdat',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\cpu0.svdat',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\cpu1.svdat',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\cpu1.svdat',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output.json',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output_mcore',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output_mcore',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output_mcore.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\expected_output_mcore.json',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\gdbinit',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\gdbinit',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\heap_log_mcore.svdat',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\heap_log_mcore.svdat',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\sysview_tracing_heap_log.elf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\sysview_tracing_heap_log.elf',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\test.elf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\test.elf',
   'DATA'),
  ('esp-idf\\tools\\esp_app_trace\\test\\sysview\\test.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_app_trace\\test\\sysview\\test.sh',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\README.md',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\esp_prov.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\esp_prov.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\proto\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\proto\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\prov\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\prov\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\prov\\custom_prov.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\prov\\custom_prov.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\prov\\wifi_ctrl.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\prov\\wifi_ctrl.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\prov\\wifi_prov.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\prov\\wifi_prov.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\prov\\wifi_scan.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\prov\\wifi_scan.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\security\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\security\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\security\\security.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\security\\security.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\security\\security0.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\security\\security0.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\security\\security1.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\security\\security1.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\security\\security2.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\security\\security2.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\security\\srp6a.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\security\\srp6a.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\transport\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\transport\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\transport\\ble_cli.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\transport\\ble_cli.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\transport\\transport.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\transport\\transport.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\transport\\transport_ble.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\transport\\transport_ble.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\transport\\transport_console.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\transport\\transport_console.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\transport\\transport_http.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\transport\\transport_http.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\utils\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\utils\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\esp_prov\\utils\\convenience.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\esp_prov\\utils\\convenience.py',
   'DATA'),
  ('esp-idf\\tools\\format.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\format.sh',
   'DATA'),
  ('esp-idf\\tools\\gdb_panic_server.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\gdb_panic_server.py',
   'DATA'),
  ('esp-idf\\tools\\gen_esp_err_to_name.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\gen_esp_err_to_name.py',
   'DATA'),
  ('esp-idf\\tools\\gen_soc_caps_kconfig\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\gen_soc_caps_kconfig\\README.md',
   'DATA'),
  ('esp-idf\\tools\\gen_soc_caps_kconfig\\gen_soc_caps_kconfig.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\gen_soc_caps_kconfig\\gen_soc_caps_kconfig.py',
   'DATA'),
  ('esp-idf\\tools\\gen_soc_caps_kconfig\\test\\test_gen_soc_caps_kconfig.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\gen_soc_caps_kconfig\\test\\test_gen_soc_caps_kconfig.py',
   'DATA'),
  ('esp-idf\\tools\\generate_debug_prefix_map.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\generate_debug_prefix_map.py',
   'DATA'),
  ('esp-idf\\tools\\idf.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf.py',
   'DATA'),
  ('esp-idf\\tools\\idf_monitor.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_monitor.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\README.md',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\constants.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\constants.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\core_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\core_ext.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\create_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\create_ext.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\debug_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\debug_ext.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\dfu_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\dfu_ext.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\errors.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\errors.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\global_options.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\global_options.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\hint_modules\\component_requirements.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\hint_modules\\component_requirements.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\hints.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\hints.yml',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\qemu_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\qemu_ext.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\roms.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\roms.json',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\roms_schema.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\roms_schema.json',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\serial_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\serial_ext.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\tools.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\tools.py',
   'DATA'),
  ('esp-idf\\tools\\idf_py_actions\\uf2_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_py_actions\\uf2_ext.py',
   'DATA'),
  ('esp-idf\\tools\\idf_size.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_size.py',
   'DATA'),
  ('esp-idf\\tools\\idf_tools.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\idf_tools.py',
   'DATA'),
  ('esp-idf\\tools\\install_util.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\install_util.py',
   'DATA'),
  ('esp-idf\\tools\\kconfig_new\\confgen.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\kconfig_new\\confgen.py',
   'DATA'),
  ('esp-idf\\tools\\kconfig_new\\config.env.in',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\kconfig_new\\config.env.in',
   'DATA'),
  ('esp-idf\\tools\\kconfig_new\\confserver.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\kconfig_new\\confserver.py',
   'DATA'),
  ('esp-idf\\tools\\kconfig_new\\prepare_kconfig_files.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\kconfig_new\\prepare_kconfig_files.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\README.md',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\entity.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\entity.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\fragments.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\fragments.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\generation.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\generation.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\ldgen_common.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\ldgen_common.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\linker_script.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\linker_script.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\output_commands.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\output_commands.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\sdkconfig.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\__pycache__\\sdkconfig.cpython-311.pyc',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\entity.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\entity.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\fragments.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\fragments.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\generation.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\generation.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\ldgen_common.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\ldgen_common.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\linker_script.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\linker_script.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\output_commands.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\output_commands.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\ldgen\\sdkconfig.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\ldgen\\sdkconfig.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\samples\\esp32.lf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\samples\\esp32.lf',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\samples\\mappings.lf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\samples\\mappings.lf',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\samples\\sdkconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\samples\\sdkconfig',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\samples\\sections.info',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\samples\\sections.info',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\samples\\template.ld',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\samples\\template.ld',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\data\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\data\\Kconfig',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\data\\base.lf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\data\\base.lf',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\data\\libfreertos.a.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\data\\libfreertos.a.txt',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\data\\linker_script.ld',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\data\\linker_script.ld',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\data\\sdkconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\data\\sdkconfig',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\data\\test_entity\\libfreertos.a.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\data\\test_entity\\libfreertos.a.txt',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\data\\test_entity\\parse_test.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\data\\test_entity\\parse_test.txt',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\test_entity.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\test_entity.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\test_fragments.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\test_fragments.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\test_generation.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\test_generation.py',
   'DATA'),
  ('esp-idf\\tools\\ldgen\\test\\test_output_commands.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\ldgen\\test\\test_output_commands.py',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\docs\\README.rst',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\docs\\README.rst',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\docs\\README_CN.rst',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\docs\\README_CN.rst',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\mfg_gen.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\mfg_gen.py',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\samples\\sample_config.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\samples\\sample_config.csv',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\samples\\sample_config_blank_lines.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\samples\\sample_config_blank_lines.csv',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\samples\\sample_values_multipage_blob.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\samples\\sample_values_multipage_blob.csv',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\samples\\sample_values_singlepage_blob.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\samples\\sample_values_singlepage_blob.csv',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\samples\\sample_values_singlepage_blob_blank_lines.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\samples\\sample_values_singlepage_blob_blank_lines.csv',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample.base64',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample.base64',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample.hex',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample.hex',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample.txt',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample_blob.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample_blob.bin',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample_encryption_keys.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample_encryption_keys.bin',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample_encryption_keys_hmac.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample_encryption_keys_hmac.bin',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample_hmac_key.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample_hmac_key.bin',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample_multipage_blob.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample_multipage_blob.bin',
   'DATA'),
  ('esp-idf\\tools\\mass_mfg\\testdata\\sample_singlepage_blob.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mass_mfg\\testdata\\sample_singlepage_blob.bin',
   'DATA'),
  ('esp-idf\\tools\\mkdfu.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mkdfu.py',
   'DATA'),
  ('esp-idf\\tools\\mkuf2.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mkuf2.py',
   'DATA'),
  ('esp-idf\\tools\\mocks\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\README.md',
   'DATA'),
  ('esp-idf\\tools\\mocks\\driver\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\driver\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\driver\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\driver\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp-tls\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp-tls\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp-tls\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp-tls\\Kconfig',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp-tls\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp-tls\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_event\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_event\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_event\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_event\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_hw_support\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_hw_support\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_hw_support\\include\\esp_etm.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_hw_support\\include\\esp_etm.h',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_hw_support\\include\\esp_intr_alloc.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_hw_support\\include\\esp_intr_alloc.h',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_hw_support\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_hw_support\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_netif\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_netif\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_netif\\include\\esp_netif_mock.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_netif\\include\\esp_netif_mock.h',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_netif\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_netif\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_partition\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_partition\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_partition\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_partition\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_timer\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_timer\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_timer\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_timer\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_wifi\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_wifi\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_wifi\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_wifi\\Kconfig',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_wifi\\global_symbols_mock.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_wifi\\global_symbols_mock.c',
   'DATA'),
  ('esp-idf\\tools\\mocks\\esp_wifi\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\esp_wifi\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\freertos\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\freertos\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\freertos\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\freertos\\Kconfig',
   'DATA'),
  ('esp-idf\\tools\\mocks\\freertos\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\freertos\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\http_parser\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\http_parser\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\http_parser\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\http_parser\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\lwip\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\lwip\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\lwip\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\lwip\\Kconfig',
   'DATA'),
  ('esp-idf\\tools\\mocks\\lwip\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\lwip\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\spi_flash\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\spi_flash\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\spi_flash\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\spi_flash\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\mocks\\startup\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\startup\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\startup\\startup_mock.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\startup\\startup_mock.c',
   'DATA'),
  ('esp-idf\\tools\\mocks\\tcp_transport\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\tcp_transport\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\mocks\\tcp_transport\\mock\\mock_config.yaml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\mocks\\tcp_transport\\mock\\mock_config.yaml',
   'DATA'),
  ('esp-idf\\tools\\python_version_checker.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\python_version_checker.py',
   'DATA'),
  ('esp-idf\\tools\\requirements.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\requirements.json',
   'DATA'),
  ('esp-idf\\tools\\requirements\\requirements.ci.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\requirements\\requirements.ci.txt',
   'DATA'),
  ('esp-idf\\tools\\requirements\\requirements.core.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\requirements\\requirements.core.txt',
   'DATA'),
  ('esp-idf\\tools\\requirements\\requirements.docs.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\requirements\\requirements.docs.txt',
   'DATA'),
  ('esp-idf\\tools\\requirements\\requirements.gdbgui.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\requirements\\requirements.gdbgui.txt',
   'DATA'),
  ('esp-idf\\tools\\requirements\\requirements.pytest.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\requirements\\requirements.pytest.txt',
   'DATA'),
  ('esp-idf\\tools\\requirements_schema.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\requirements_schema.json',
   'DATA'),
  ('esp-idf\\tools\\set-submodules-to-github.sh',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\set-submodules-to-github.sh',
   'DATA'),
  ('esp-idf\\tools\\split_paths_by_spaces.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\split_paths_by_spaces.py',
   'DATA'),
  ('esp-idf\\tools\\templates\\sample_component\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\templates\\sample_component\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\templates\\sample_component\\include\\main.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\templates\\sample_component\\include\\main.h',
   'DATA'),
  ('esp-idf\\tools\\templates\\sample_component\\main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\templates\\sample_component\\main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\anti_rollback_partition.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\anti_rollback_partition.csv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\main\\build_system_bootloader_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\main\\build_system_bootloader_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.anti_roll_back',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.anti_roll_back',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.factory_reset',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.factory_reset',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.secure_boot.ecdsa.esp32h2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.secure_boot.ecdsa.esp32h2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.verbose_logging',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.ci.verbose_logging',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.defaults.esp32',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\bootloader\\sdkconfig.defaults.esp32',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\components\\custom\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\components\\custom\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\components\\custom\\project_include.cmake',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\components\\custom\\project_include.cmake',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\main\\test_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\main\\test_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\partitions.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\partitions.csv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\custom_partition_subtypes\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\README.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\README.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\2file.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\2file.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\_file.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\_file.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\file.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\file.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\test_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\embed_test\\main\\test_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\main\\linker.lf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\main\\linker.lf',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\main\\test_app_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\main\\test_app_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\pytest_ld_non_contiguous_memory.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ld_non_contiguous_memory\\pytest_ld_non_contiguous_memory.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\check_placements.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\check_placements.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\linker.lf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\linker.lf',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\src1.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\src1.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\src2.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\src2.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\test_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\build_system\\ldgen_test\\main\\test_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\configs\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\configs\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\configs\\sdkconfig.debug_helpers',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\configs\\sdkconfig.debug_helpers',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\main\\generic_build_test.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\generic_build_test\\main\\generic_build_test.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\freertos_test_utils.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\freertos_test_utils.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\freertos_test_utils.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\freertos_test_utils.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\portTestMacro.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\portTestMacro.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\port\\test_tlsp_del_cb.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\port\\test_tlsp_del_cb.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\queue\\test_queuesets.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\queue\\test_queuesets.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\stream_buffer\\test_stream_buffers.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\stream_buffer\\test_stream_buffers.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_eTaskGetState.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_eTaskGetState.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_freertos_task_delete.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_freertos_task_delete.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_preemption.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_preemption.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_priority_scheduling.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_priority_scheduling.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_task_delay.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_task_delay.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_task_priorities.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_task_priorities.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_yielding.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\tasks\\test_yielding.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\timers\\test_timers.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\components\\kernel_tests\\timers\\test_timers.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\main\\Kconfig.projbuild',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\main\\Kconfig.projbuild',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\main\\linux_freertos.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\main\\linux_freertos.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\pytest_linux_freertos.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\pytest_linux_freertos.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\linux_freertos\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\main\\mock_build_test.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\main\\mock_build_test.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\linux_compatible\\mock_build_test\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\peripherals\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\peripherals\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\main\\Kconfig.projbuild',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\main\\Kconfig.projbuild',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\main\\i2c_wifi_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\main\\i2c_wifi_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\pytest_i2c_wifi.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\peripherals\\i2c_wifi\\pytest_i2c_wifi.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\main\\phy_init_data_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\main\\phy_init_data_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\pytest_phy_multi_init_data.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\pytest_phy_multi_init_data.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\sdkconfig.ci.phy_multiple_init_data',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\sdkconfig.ci.phy_multiple_init_data',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\sdkconfig.ci.phy_multiple_init_data_embed',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_multi_init_data_test\\sdkconfig.ci.phy_multiple_init_data_embed',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\app_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\app_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\tsens_cmd.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\tsens_cmd.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\wifi_cmd.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\main\\wifi_cmd.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\pytest_phy_tsens.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\pytest_phy_tsens.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\sdkconfig.ci',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\sdkconfig.ci',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\sdkconfig.ci.c2_xtal26m',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\sdkconfig.ci.c2_xtal26m',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\phy\\phy_tsens\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\phy\\phy_tsens\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\init_macro.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\init_macro.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\main.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\main.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\netif_init_c99.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\netif_init_c99.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\netif_init_cpp.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\main\\netif_init_cpp.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\esp_netif\\build_config\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\main\\mqtt_app.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\build_test\\main\\mqtt_app.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\ca.crt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\ca.crt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\ca.der',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\ca.der',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\ca.key',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\ca.key',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_inv.crt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_inv.crt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_no_pwd.key',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_no_pwd.key',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_pwd.crt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_pwd.crt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_pwd.key',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\client_pwd.key',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\Kconfig.projbuild',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\Kconfig.projbuild',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\connect_test.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\connect_test.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\mqtt_eclipseprojects_io.pem',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\mqtt_eclipseprojects_io.pem',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\publish_connect_test.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\publish_connect_test.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\publish_connect_test.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\publish_connect_test.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\publish_test.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\main\\publish_test.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\pytest_mqtt_app.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\pytest_mqtt_app.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\pytest_mqtt_publish_app.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\pytest_mqtt_publish_app.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\sdkconfig.ci.default',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\sdkconfig.ci.default',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\sdkconfig.qemu',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\sdkconfig.qemu',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\server.key',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\mqtt\\publish_connect_test\\server.key',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\esp_netif_stack\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\esp_netif_stack\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\main\\Kconfig.projbuild',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\main\\Kconfig.projbuild',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\main\\netif_components.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\main\\netif_components.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\sdkconfig.ci.esp_netif',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\sdkconfig.ci.esp_netif',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\sdkconfig.ci.esp_netif_nolwip',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\sdkconfig.ci.esp_netif_nolwip',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\netif_components\\sdkconfig.ci.lwip',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\netif_components\\sdkconfig.ci.lwip',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\network_tests\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\network_tests\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\network_tests\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\network_tests\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\net_suite.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\net_suite.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\stdinout.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\stdinout.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\stdinout.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\protocols\\network_tests\\main\\stdinout.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\conftest.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\main\\secure_boot_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\main\\secure_boot_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\main\\secure_boot_main_esp32.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\main\\secure_boot_main_esp32.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\pytest_secure_boot.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\pytest_secure_boot.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.00',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.00',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.01',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.01',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.02',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.02',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.03',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.03',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.04',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.ci.04',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\test_ecdsa_key.pem',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\test_ecdsa_key.pem',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\secure_boot\\test_rsa_3072_key.pem',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\secure_boot\\test_rsa_3072_key.pem',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\main\\main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\main\\main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\pytest_signed_app_no_secure_boot.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\pytest_signed_app_no_secure_boot.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\security\\signed_app_no_secure_boot\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\filesystem_image\\dir\\dirf.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\filesystem_image\\dir\\dirf.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\filesystem_image\\hello.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\filesystem_image\\hello.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\main\\main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\main\\main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\nvs_data.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\nvs_data.csv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\partitions_example.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\partitions_example.csv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\pytest_partition_table_readonly.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\pytest_partition_table_readonly.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\sdkconfig.ci.default',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\sdkconfig.ci.default',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\sdkconfig.ci.encrypted',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\sdkconfig.ci.encrypted',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\partition_table_readonly\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_sdmmc\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_sdmmc\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_sdmmc\\cmd_sdmmc.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_sdmmc\\cmd_sdmmc.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_sdmmc\\cmd_sdmmc.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_sdmmc\\cmd_sdmmc.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_unity\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_unity\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_unity\\cmd_unity.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_unity\\cmd_unity.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_unity\\cmd_unity.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\components\\cmd_unity\\cmd_unity.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\main\\sdmmc_console_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\main\\sdmmc_console_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\partitions.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\partitions.csv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\sdkconfig.defaults.esp32h2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\sdkconfig.defaults.esp32h2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\sdkconfig.defaults.esp32p4',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\storage\\sdmmc_console\\sdkconfig.defaults.esp32p4',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\.build-test-rules.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\.build-test-rules.yml',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\main\\test_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\main\\test_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\partitions_example.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\partitions_example.csv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.anti_rollback',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.anti_rollback',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.default',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.default',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.flash_encryption',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.flash_encryption',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.rtc_retain',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.ci.rtc_retain',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\bootloader_sections\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\main\\test_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\main\\test_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.client_only_mbedtls',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.client_only_mbedtls',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.console_none_esp32c3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.console_none_esp32c3',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.custom_mac',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.custom_mac',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.custom_uart',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.custom_uart',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.esp32c2_26mhz_xtal',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.esp32c2_26mhz_xtal',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.esp32s3_mspi_timing_assertion_disabled',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.esp32s3_mspi_timing_assertion_disabled',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.ethernet_disabled',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.ethernet_disabled',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.flash_encryption_release',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.flash_encryption_release',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.freertos_smp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.freertos_smp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.heap_tracing',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.heap_tracing',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_esp_cert_bundle',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_esp_cert_bundle',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_flash_delay',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_flash_delay',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_https_client',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_https_client',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_hwsg',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_hwsg',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_rvfplib',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.no_rvfplib',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.o2_no_asserts',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.o2_no_asserts',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.panic_handler_iram',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.panic_handler_iram',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.panic_silent_reboot',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.panic_silent_reboot',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.phy_multiple_init_data',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.phy_multiple_init_data',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.spi_flash_opts',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.spi_flash_opts',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.trax_esp32',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.trax_esp32',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.trax_esp32s2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.trax_esp32s2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.usb_console_ets_printf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\build_test\\sdkconfig.ci.usb_console_ets_printf',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_build_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_build_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_build_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_build_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\cxx_build_test_main.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\cxx_build_test_main.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\test_cxx_standard.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\test_cxx_standard.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\test_i2c_lcd.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\test_i2c_lcd.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\test_soc_reg_macros.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_build_test\\main\\test_soc_reg_macros.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_no_except\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_no_except\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_no_except\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_no_except\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_no_except\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_no_except\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_no_except\\main\\main.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_no_except\\main\\main.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_no_except\\sdkconfig.ci.riscv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_no_except\\sdkconfig.ci.riscv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_no_except\\sdkconfig.ci.xtensa',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_no_except\\sdkconfig.ci.xtensa',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\main\\cpp_pthread.cpp',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\main\\cpp_pthread.cpp',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\sdkconfig.ci.clang_libclang_rt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\sdkconfig.ci.clang_libclang_rt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\sdkconfig.ci.clang_libgcc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\sdkconfig.ci.clang_libgcc',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\cxx_pthread_bluetooth\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\eh_frame\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\eh_frame\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\eh_frame\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\eh_frame\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\eh_frame\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\eh_frame\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\eh_frame\\main\\eh_frame_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\eh_frame\\main\\eh_frame_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\eh_frame\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\eh_frame\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32c2.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32c2.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32c3.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32c3.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32c6.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32c6.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32h2.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32h2.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32p4.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32p4.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32s2.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32s2.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32s3.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\expected_output\\esp32s3.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\main\\test_esp_intr_dump_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\main\\test_esp_intr_dump_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\pytest_esp_intr_dump.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\esp_intr_dump\\pytest_esp_intr_dump.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\g0_components\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\g0_components\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\g0_components\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\g0_components\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\g0_components\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\g0_components\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\g1_components\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\g1_components\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\g1_components\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\g1_components\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\g1_components\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\g1_components\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\g1_components\\main\\g1_components.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\g1_components\\main\\g1_components.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb\\main\\hello_world_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb\\main\\hello_world_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb\\pytest_gdb.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb\\pytest_gdb.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\conftest.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\gdbinit_esp32',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\gdbinit_esp32',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\gdbinit_esp32c3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\gdbinit_esp32c3',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\gdbinit_esp32s2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\gdbinit_esp32s2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\main\\hello_world_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\main\\hello_world_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\pytest_gdb_loadable_elf.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\pytest_gdb_loadable_elf.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\test_gdb_loadable_elf_util\\loadable_app_serial.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdb_loadable_elf\\test_gdb_loadable_elf_util\\loadable_app_serial.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\conftest.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\main\\test_app_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\main\\test_app_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\pytest_runtime.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\pytest_runtime.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\gdbstub_runtime\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\longjmp_test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\longjmp_test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\longjmp_test\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\longjmp_test\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\longjmp_test\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\longjmp_test\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\longjmp_test\\main\\hello_world_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\longjmp_test\\main\\hello_world_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\longjmp_test\\pytest_longjmp.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\longjmp_test\\pytest_longjmp.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\longjmp_test\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\longjmp_test\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\Kconfig.projbuild',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\Kconfig.projbuild',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32c3\\return_from_panic.S',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32c3\\return_from_panic.S',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32c3\\test_memprot_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32c3\\test_memprot_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32c3\\test_panic.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32c3\\test_panic.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s2\\test_memprot_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s2\\test_memprot_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s2\\test_panic.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s2\\test_panic.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s3\\test_memprot_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s3\\test_memprot_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s3\\test_panic.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\main\\esp32s3\\test_panic.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\pytest_memprot.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\pytest_memprot.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\memprot\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\memprot\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\check_for_file_paths.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\check_for_file_paths.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\main\\test_no_embedded_paths_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\main\\test_no_embedded_paths_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.noasserts',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.noasserts',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.noasserts.nimble',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.noasserts.nimble',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.replacepaths',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.replacepaths',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.silentasserts',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.silentasserts',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.silentasserts.nimble',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\no_embedded_paths\\sdkconfig.ci.silentasserts.nimble',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\conftest.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\Kconfig.projbuild',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\Kconfig.projbuild',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\include\\test_memprot.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\include\\test_memprot.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\include\\test_panic.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\include\\test_panic.h',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\panic_utils\\memprot_panic_utils_riscv.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\panic_utils\\memprot_panic_utils_riscv.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\panic_utils\\memprot_panic_utils_xtensa.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\panic_utils\\memprot_panic_utils_xtensa.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\test_app_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\test_app_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\test_memprot.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\test_memprot.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\main\\test_panic.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\main\\test_panic.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\panic_utils.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\panic_utils.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\partitions_capture_dram.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\partitions_capture_dram.csv',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\pytest_panic.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\pytest_panic.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_extram_stack',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_extram_stack',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_bin_crc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_bin_crc',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_capture_dram',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_capture_dram',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_custom_stack',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_custom_stack',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_elf_sha',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_flash_elf_sha',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_uart_bin_crc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_uart_bin_crc',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_uart_elf_crc',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.coredump_uart_elf_crc',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.gdbstub',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.gdbstub',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.gdbstub_coredump',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.gdbstub_coredump',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32c2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32c2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32c3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32c3',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32c6',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32c6',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32h2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32h2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32p4',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32p4',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32s2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32s2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32s3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.memprot_esp32s3',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.panic',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.panic',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.panic_delay',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.ci.panic_delay',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\test_panic_util\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\test_panic_util\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\panic\\test_panic_util\\panic_dut.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\panic\\test_panic_util\\panic_dut.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\main\\ram_loadable_app_test.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\main\\ram_loadable_app_test.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\pytest_ram_loadable_app.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\pytest_ram_loadable_app.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\sdkconfig.ci.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\sdkconfig.ci.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\sdkconfig.ci.pure_ram',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\sdkconfig.ci.pure_ram',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\ram_loadable_app\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\main\\rtc_mem_reserve_test_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\main\\rtc_mem_reserve_test_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\main\\ulp\\main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\main\\ulp\\main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\pytest_rtc_mem_reserve.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\pytest_rtc_mem_reserve.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\rtc_mem_reserve\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\README.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\README.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\main\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\main\\Kconfig',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\main\\chip_info_patch.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\main\\chip_info_patch.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\main\\test_startup_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\main\\test_startup_main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\pytest_startup.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\pytest_startup.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.flash_80m_qio',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.flash_80m_qio',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.main_task_cpu1',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.main_task_cpu1',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.no_vfs',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.no_vfs',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.no_vfs_partial',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.no_vfs_partial',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.opt_o0',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.opt_o0',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.opt_o2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.opt_o2',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.single_core_variant',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.single_core_variant',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.sram1_iram',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.sram1_iram',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.stack_check_verbose_log',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\startup\\sdkconfig.ci.stack_check_verbose_log',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\test_watchpoint\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\test_watchpoint\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\test_watchpoint\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\test_watchpoint\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\test_watchpoint\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\test_watchpoint\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\test_watchpoint\\main\\main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\test_watchpoint\\main\\main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\test_watchpoint\\pytest_watchpoint.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\test_watchpoint\\pytest_watchpoint.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\test_watchpoint\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\test_watchpoint\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\README.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\README.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\conftest.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\main\\main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\main\\main.c',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\pytest_unicore_bootloader.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\pytest_unicore_bootloader.py',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\sdkconfig.ci.multicore',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\sdkconfig.ci.multicore',
   'DATA'),
  ('esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\sdkconfig.ci.unicore',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_apps\\system\\unicore_bootloader\\sdkconfig.ci.unicore',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\build_test_app\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\build_test_app\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\build_test_app\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\build_test_app\\README.md',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\build_test_app\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\build_test_app\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\build_test_app\\main\\build_test_app.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\build_test_app\\main\\build_test_app.c',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\conftest.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\conftest.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\pytest.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\pytest.ini',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_bootloader.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_bootloader.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_build.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_build.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_build_system_helpers\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_build_system_helpers\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_build_system_helpers\\build_constants.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_build_system_helpers\\build_constants.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_build_system_helpers\\editing.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_build_system_helpers\\editing.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_build_system_helpers\\idf_utils.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_build_system_helpers\\idf_utils.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_build_system_helpers\\snapshot.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_build_system_helpers\\snapshot.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_cmake.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_cmake.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_common.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_common.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_component_manager.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_component_manager.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_components.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_components.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_git.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_git.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_kconfig.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_kconfig.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_non_default_target.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_non_default_target.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_partition.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_partition.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_rebuild.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_rebuild.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_sdkconfig.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_sdkconfig.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_spaces.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_spaces.py',
   'DATA'),
  ('esp-idf\\tools\\test_build_system\\test_versions.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_build_system\\test_versions.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\error_output.yml',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\error_output.yml',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\extra_path\\some_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\extra_path\\some_ext.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_a',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_a',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_b',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_b',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_circular_a',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_circular_a',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_circular_b',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_circular_b',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_recursive',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\file_args_expansion_inputs\\args_recursive',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\idf_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\idf_ext.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\idf_py_help_schema.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\idf_py_help_schema.json',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\pytest.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\pytest.ini',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\test_hints.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\test_hints.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\test_idf_extensions\\test_ext\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\test_idf_extensions\\test_ext\\__init__.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\test_idf_extensions\\test_ext\\test_extension.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\test_idf_extensions\\test_ext\\test_extension.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\test_idf_py.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\test_idf_py.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_py\\test_idf_qemu.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_py\\test_idf_qemu.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_size\\pytest.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_size\\pytest.ini',
   'DATA'),
  ('esp-idf\\tools\\test_idf_size\\test_idf_size.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_size\\test_idf_size.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\add_version\\artifact_expected_addition.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\add_version\\artifact_expected_addition.json',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\add_version\\artifact_input.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\add_version\\artifact_input.json',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\add_version\\checksum.sha256',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\add_version\\checksum.sha256',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\add_version\\checksum_expected_addition.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\add_version\\checksum_expected_addition.json',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\add_version\\checksum_expected_override.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\add_version\\checksum_expected_override.json',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\platform_detection\\arm32_header.elf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\platform_detection\\arm32_header.elf',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\platform_detection\\arm64_header.elf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\platform_detection\\arm64_header.elf',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\platform_detection\\armhf_header.elf',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\platform_detection\\armhf_header.elf',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\test_idf_tools.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\test_idf_tools.py',
   'DATA'),
  ('esp-idf\\tools\\test_idf_tools\\test_idf_tools_python_env.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_idf_tools\\test_idf_tools_python_env.py',
   'DATA'),
  ('esp-idf\\tools\\test_mkdfu\\1\\1.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_mkdfu\\1\\1.bin',
   'DATA'),
  ('esp-idf\\tools\\test_mkdfu\\1\\2.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_mkdfu\\1\\2.bin',
   'DATA'),
  ('esp-idf\\tools\\test_mkdfu\\1\\3.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_mkdfu\\1\\3.bin',
   'DATA'),
  ('esp-idf\\tools\\test_mkdfu\\1\\dfu.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_mkdfu\\1\\dfu.bin',
   'DATA'),
  ('esp-idf\\tools\\test_mkdfu\\2\\dfu.bin',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_mkdfu\\2\\dfu.bin',
   'DATA'),
  ('esp-idf\\tools\\test_mkdfu\\pytest.ini',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_mkdfu\\pytest.ini',
   'DATA'),
  ('esp-idf\\tools\\test_mkdfu\\test_mkdfu.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\test_mkdfu\\test_mkdfu.py',
   'DATA'),
  ('esp-idf\\tools\\tools.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\tools.json',
   'DATA'),
  ('esp-idf\\tools\\tools_schema.json',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\tools_schema.json',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\README.md',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\README.md',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\Kconfig',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\Kconfig',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\include\\memory_checks.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\include\\memory_checks.h',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\include\\test_utils.h',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\include\\test_utils.h',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\memory_checks.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\memory_checks.c',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\ref_clock_impl_rmt_pcnt.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\ref_clock_impl_rmt_pcnt.c',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\ref_clock_impl_timergroup.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\ref_clock_impl_timergroup.c',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\test\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\test\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\test\\leak_test.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\test\\leak_test.c',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\test_runner.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\test_runner.c',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\components\\test_utils\\test_utils.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\components\\test_utils\\test_utils.c',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\configs\\.gitkeep',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\configs\\.gitkeep',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\idf_ext.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\idf_ext.py',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\main\\CMakeLists.txt',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\main\\CMakeLists.txt',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\main\\app_main.c',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\main\\app_main.c',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\partition_table_unit_test_app.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\partition_table_unit_test_app.csv',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\partition_table_unit_test_app_2m.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\partition_table_unit_test_app_2m.csv',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\partition_table_unit_test_two_ota.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\partition_table_unit_test_two_ota.csv',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\partition_table_unit_test_two_ota_2m.csv',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\partition_table_unit_test_two_ota_2m.csv',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\sdkconfig.defaults',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\sdkconfig.defaults',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32c2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32c2',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32c3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32c3',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32s2',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32s2',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32s3',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\sdkconfig.defaults.esp32s3',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\tools\\CreateSectionTable.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\tools\\CreateSectionTable.py',
   'DATA'),
  ('esp-idf\\tools\\unit-test-app\\tools\\ElfUnitTestParser.py',
   'D:\\software\\code\\FlashToolApp\\esp-idf\\tools\\unit-test-app\\tools\\ElfUnitTestParser.py',
   'DATA'),
  ('esp_secure_cert\\__init__.py',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__init__.py',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\__init__.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\configure_ds.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\configure_ds.cpython-311.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\configure_ds.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\configure_ds.cpython-312.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\custflash_format.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\custflash_format.cpython-311.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\custflash_format.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\custflash_format.cpython-312.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\efuse_helper.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\efuse_helper.cpython-311.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\efuse_helper.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\efuse_helper.cpython-312.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\esp_secure_cert_helper.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\esp_secure_cert_helper.cpython-311.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\esp_secure_cert_helper.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\esp_secure_cert_helper.cpython-312.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\nvs_format.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\nvs_format.cpython-311.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\nvs_format.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\nvs_format.cpython-312.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\tlv_format.cpython-311.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\tlv_format.cpython-311.pyc',
   'DATA'),
  ('esp_secure_cert\\__pycache__\\tlv_format.cpython-312.pyc',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\__pycache__\\tlv_format.cpython-312.pyc',
   'DATA'),
  ('esp_secure_cert\\configure_ds.py',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\configure_ds.py',
   'DATA'),
  ('esp_secure_cert\\custflash_format.py',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\custflash_format.py',
   'DATA'),
  ('esp_secure_cert\\efuse_helper.py',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\efuse_helper.py',
   'DATA'),
  ('esp_secure_cert\\esp_secure_cert_helper.py',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\esp_secure_cert_helper.py',
   'DATA'),
  ('esp_secure_cert\\nvs_format.py',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\nvs_format.py',
   'DATA'),
  ('esp_secure_cert\\tlv_format.py',
   'D:\\software\\code\\FlashToolApp\\esp_secure_cert\\tlv_format.py',
   'DATA'),
  ('esptool\\targets\\stub_flasher\\1\\esp32s3.json',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\stub_flasher\\1\\esp32s3.json',
   'DATA'),
  ('esptool\\targets\\stub_flasher\\2\\esp32s3.json',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\stub_flasher\\2\\esp32s3.json',
   'DATA'),
  ('hyplink\\bootloader.bin',
   'D:\\software\\code\\FlashToolApp\\hyplink\\bootloader.bin',
   'DATA'),
  ('hyplink\\hyplink-bc.bin',
   'D:\\software\\code\\FlashToolApp\\hyplink\\hyplink-bc.bin',
   'DATA'),
  ('hyplink\\hyplink.bin',
   'D:\\software\\code\\FlashToolApp\\hyplink\\hyplink.bin',
   'DATA'),
  ('hyplink\\hyplink.bin.bak',
   'D:\\software\\code\\FlashToolApp\\hyplink\\hyplink.bin.bak',
   'DATA'),
  ('hyplink\\ota_data_initial.bin',
   'D:\\software\\code\\FlashToolApp\\hyplink\\ota_data_initial.bin',
   'DATA'),
  ('hyplink\\partition-table.bin',
   'D:\\software\\code\\FlashToolApp\\hyplink\\partition-table.bin',
   'DATA'),
  ('hyplink\\rcp_fw.bin',
   'D:\\software\\code\\FlashToolApp\\hyplink\\rcp_fw.bin',
   'DATA'),
  ('hyplink\\spiffs_storage.bin',
   'D:\\software\\code\\FlashToolApp\\hyplink\\spiffs_storage.bin',
   'DATA'),
  ('hypsensor\\HYP-60-bc.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\HYP-60-bc.bin',
   'DATA'),
  ('hypsensor\\HYP-60.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\HYP-60.bin',
   'DATA'),
  ('hypsensor\\HYP-60.bin.bak',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\HYP-60.bin.bak',
   'DATA'),
  ('hypsensor\\config.ini',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\config.ini',
   'DATA'),
  ('hypsensor\\hyp225-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp225-mcuboot.bin',
   'DATA'),
  ('hypsensor\\hyp225v2-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp225v2-mcuboot.bin',
   'DATA'),
  ('hypsensor\\hyp225v3-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp225v3-mcuboot.bin',
   'DATA'),
  ('hypsensor\\hyp60-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60-mcuboot.bin',
   'DATA'),
  ('hypsensor\\hyp60v2-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60v2-mcuboot.bin',
   'DATA'),
  ('hypsensor\\hyp60v3-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60v3-mcuboot.bin',
   'DATA'),
  ('hypsensor\\hyp60v4-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hyp60v4-mcuboot.bin',
   'DATA'),
  ('hypsensor\\hypx-mcuboot.bin',
   'D:\\software\\code\\FlashToolApp\\hypsensor\\hypx-mcuboot.bin',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\METADATA',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\licenses\\LICENSE',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\INSTALLER',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\RECORD',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\WHEEL',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.5.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.5\\tcltest-2.5.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\LICENSE.txt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.3.0.dist-info\\DELVEWHEEL',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\RECORD',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.0.dist-info\\INSTALLER',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.0.dist-info\\entry_points.txt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\WHEEL',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.0.dist-info\\METADATA',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'D:\\software\\code\\FlashToolApp\\build\\FastFlasher\\base_library.zip',
   'DATA')],)

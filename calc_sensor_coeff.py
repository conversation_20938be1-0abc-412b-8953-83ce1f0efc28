#!/usr/bin/env python3
"""
Compute (uV_threshold, gain, offset) for a current sensor.
 * 阈值先按 'µV = k*I + b' 线性拟合，在 20 A 处取值；
   接着按 <step> µV 向上取整 (默认 5 000)。
 * gain/offset 默认用 **全部数据** 做线性回归，可用 --fit-scope above 改为仅高区段。
 * Excel 表需含三列：
       实际微伏uV | 实际电流A | 读出电流A
Usage examples
    python calc_sensor_coeff.py test.xlsx
    python calc_sensor_coeff.py test.xlsx --fit-scope above -s 1000
    python calc_sensor_coeff.py test.xlsx -t 150000          # 手动阈值
"""

import argparse
from pathlib import Path

import numpy as np
import pandas as pd


def load_df(path):
    df = pd.read_excel(path)
    # Trim spaces in headers
    df.columns = [c.strip() for c in df.columns]
    # 兼容不同的“读出电流”列名
    for cand in ("读出电流A", "读out电流A", "readA"):
        if cand in df.columns:
            ir_col = cand
            break
    else:
        raise ValueError("未找到“读出电流”列")
    return df, "实际微伏uV", "实际电流A", ir_col


def calc_threshold(df, uv_col, ia_col, step, manual=None):
    if manual:
        return manual
    # 全量线性拟合 µV = k * I + b
    k, b = np.polyfit(df[ia_col], df[uv_col], 1)
    uv_at_20 = k * 20 + b
    # 向上取整到 step
    return int(np.ceil(uv_at_20 / step) * step)


def fit_gain_offset(df, ir_col, ia_col, uv_col, uv_thresh, scope):
    if scope == "above":
        mask = df[uv_col] > uv_thresh
        if not mask.any():
            raise RuntimeError("阈值过高，未找到可用于回归的数据")
        x, y = df.loc[mask, ir_col], df.loc[mask, ia_col]
    else:  # "all"
        x, y = df[ir_col], df[ia_col]
    return np.polyfit(x, y, 1)  # 返回 gain, offset


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("excel", help="测试数据表")
    ap.add_argument("-t", "--thresh", type=int,
                    help="手动指定 uV_threshold (µV)")
    ap.add_argument("-s", "--step", type=int, default=5000,
                    help="阈值取整步长，默认 5000 µV")
    ap.add_argument("--fit-scope", choices=("all", "above"),
                    default="all",
                    help="all=全量数据回归  above=仅高区段回归")
    args = ap.parse_args()

    df, uv_col, ia_col, ir_col = load_df(args.excel)

    uv_thresh = calc_threshold(df, uv_col, ia_col,
                               args.step, args.thresh)
    gain, offset = fit_gain_offset(df, ir_col, ia_col,
                                   uv_col, uv_thresh,
                                   args.fit_scope)

    df.insert(df.columns.get_loc(ir_col) + 1,        # 在“读出电流”后一列
              "校正前绝对误差A",
              (df[ir_col] - df[ia_col]).abs())

    df.insert(df.columns.get_loc("校正前绝对误差A") + 1,
              "校正前误差百分比",
              (df["校正前绝对误差A"] / df[ia_col] * 100).round(3))

    # 校正并统计
    df["校正电流A"] = np.where(df[uv_col] > uv_thresh,
                               gain * df[ir_col] + offset,
                               df[ir_col])
    df["校正后绝对误差A"] = (df["校正电流A"] - df[ia_col]).abs()
    df["校正后误差百分比"] = (df["校正后绝对误差A"] / df[ia_col] * 100).round(3)

    print(f"uV_threshold = {uv_thresh:.0f} µV")
    print(f"gain         = {gain:.7f}")
    print(f"offset       = {offset:.7f} A")
    print("-" * 40)
    print(f"平均绝对误差  = {df['校正后绝对误差A'].mean():.3f} A")
    print(f"最大绝对误差  = {df['校正后绝对误差A'].max():.3f} A")

    out_path = Path(args.excel).with_stem(
        Path(args.excel).stem + "_calibrated")
    df.to_excel(out_path, index=False)
    print(f"结果已写入: {out_path}")


if __name__ == "__main__":
    main()
